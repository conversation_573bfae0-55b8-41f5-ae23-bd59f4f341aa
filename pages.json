{
    "pages": [
      //pages数组中第一项表示应用启动页,
      //管理
      {
        "path": "pages/index/index",
        "style": {
          // #ifndef MP-ALIPAY
          "navigationBarTitleText": "小葫芦",
          // #endif
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "always",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/index/my",
        "style": {
          "navigationBarTitleText": "",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "always",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/home/<USER>",
        "style": {
          "navigationBarTitleText": "首页",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "always",
            "titlePenetrate": "YES"
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          }
        }
      },
      // #ifdef MP-ALIPAY
      {
        "path": "pages/lights/index",
        "style": {
          "navigationBarTitleText": "首页-lights",
          "navigationStyle": "custom",
          "usingComponents": {
            "ad": "/mycomponents/ad/index",
            "private-pouch-component-pop": "/mycomponents/private-pouch-component-pop/index"
          },
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "none",
            "titlePenetrate": "YES",
            "usingComponents": {
              "ad-modal-component": "plugin://xlight/ad-modal-component",
              "private-pouch-pop": "plugin://xlight/private-pouch-pop"
            }
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          }
        }
      },
      // #endif
      {
        "path": "pages/result/index",
        "style": {
          "navigationBarTitleText": "出袋结果",
          "navigationStyle": "custom",
          "app-plus": {
            "titleNView": false
          }
        }
      }
        ,{
            "path" : "pages/freeOut/index",
            "style" :
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }

        },
        {
            "path" : "pages/resultLoading/index",
            "style" :
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        },
        // #ifdef MP-ALIPAY
        {
          "path" : "pages/lights/packet-result",
          "style" :
          {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        // #endif
        {
          "path": "pages/personal/index",
          "style": {
            "navigationBarTitleText": "我的",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            },
            "mp-alipay": {
              "allowsBounceVertical": "NO",
              // 将回弹属性关掉
              "bounce": "none",
              "titleNView": false,
              "transparentTitle": "always",
              "titlePenetrate": "YES"
            }
          }
        },
        {
          "path": "pages/news/index",
          "style": {
            "navigationBarTitleText": "消息",
            "enablePullDownRefresh": false,
            //        "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            },
            "mp-alipay": {
              "allowsBounceVertical": "NO",
              // 将回弹属性关掉
              "bounce": "none",
              "titleNView": false,
              "transparentTitle": "none",
              "titlePenetrate": "YES"
            }
          }
        },
      {
        "path": "pages/circle-home/index",
        "style": {
          // #ifndef MP-ALIPAY
          "navigationBarTitleText": "交流",
          // #endif
          "enablePullDownRefresh": false,
          "navigationBarBackgroundColor":"#F4F6FA",
          "navigationStyle": "custom",
          "app-plus": {
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "always",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/post-message/index",
        "style": {
          // #ifndef MP-ALIPAY
          "navigationBarTitleText": "发帖",
          // #endif
          "enablePullDownRefresh": false,
          "navigationStyle": "custom",
          "app-plus": {
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "always",
            "titlePenetrate": "YES"
          }
        }
      },
      // 卡片出袋
      {
        "path": "pages/packet/index",
        "style": {
          "navigationBarTitleText": "欢迎使用绿葆自助取袋机",
          "navigationStyle": "custom"
        }
      },
      {
        "path": "pages/packet/pay/index",
        "style": {
          "navigationBarTitleText": "支付出袋",
          "navigationStyle": "custom"
        }
      },
      {
        "path": "pages/packet/MiddlePage",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "欢迎使用绿葆自助取袋机"
        }
      },
      {
        "path": "pages/web-html-view/index",
        "style": {
          "navigationBarTitleText": "欢迎使用绿葆自助取袋机"
        }
      },
      {
        "path": "pages/redpacket/index",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "做任务领红包"
        }
      },
      {
        "path": "pages/redpacket/receive",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "做任务领红包"
        }
      },
      {
        "path": "pages/packet-dll/index",
        "style": {
          "navigationBarTitleText": "取袋宝"
        }
      },
      {
        "path": "pages/packet-dll/result",
        "style": {
          "navigationBarTitleText": "取袋宝"
        }
      }
    ],
    "subPackages": [
      {
        "root": "modules/common",
        "pages": [
          {
            "path": "positioning/positioning",
            "style": {
              "navigationBarTitleText": "定位",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "positioning/index",
            "style": {
              "navigationBarTitleText": "选择地址",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "positioning/positioning-search",
            "style": {
              "navigationBarTitleText": "选择地址",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "system-search/index",
            "style": {
              "navigationBarTitleText": "搜索",
              //            "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "system-search/search-data",
            "style": {
              "navigationBarTitleText": "搜索",
              //            "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "switch-roles/index",
            "style": {
              "navigationBarTitleText": "切换角色",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "list/phone-multi-select-list",
            "style": {
              "navigationBarTitleText": "选择人员",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          // web-view通用第三方页面
          {
            "path": "web-html-view/index",
            "style": {
              "navigationBarTitleText": "",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          // web-view通用第三方页面
          {
            "path": "instruction-web-html-view/index",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff",
                  "buttons": [ //原生标题栏按钮配置,
                      {
                      	"type": "back", //这就是  1 部分的返回按钮
                      	"float": "left", //这个我相信大家都能看懂吧。
                      	"fontSize": "20px"
                      }
                  ]
                }
              }
            }
          },
          {
            "path": "pc-view/index",
            "style": {
              "navigationBarTitleText": "登录PC后台",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "app-jump-view/index",
            "style": {
              "navigationBarTitleText": "正在前往第三方小程序...",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root": "modules/system",
        "pages": [{
          "path": "receipt-inform/index",
          "style": {
            "navigationBarTitleText": "接收通知",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
          {
            "path": "login/index",
            "style": {
              "navigationBarTitleText": "用户登录",
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "reg/index",
            "style": {
              "navigationBarTitleText": "",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pwd/index",
            "style": {
              "navigationBarTitleText": "找回密码",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "user/index",
            "style": {
              "navigationBarTitleText": "编辑资料",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "user-introduce/index",
            "style": {
              "navigationBarTitleText": "编辑个人介绍",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "help-center/index",
            "style": {
              "navigationBarTitleText": "帮助中心",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "setting/index",
            "style": {
              "navigationBarTitleText": "系统设置",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "setting/about/index",
            "style": {
              "navigationBarTitleText": "关于绿葆",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "modify-phone/index",
            "style": {
              "navigationBarTitleText": "修改手机号码",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "modify-phone/bind-phone",
            "style": {
              "navigationBarTitleText": "绑定新手机号码",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "verification-code/index",
            "style": {
              "navigationBarTitleText": "",
              //"navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "invalid/not-find",
            "style": {
              "navigationBarTitleText": "网络失联了",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "feedback/index",
            "style": {
              "navigationBarTitleText": "意见反馈",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "my-name/index",
            "style": {
              "navigationBarTitleText": "姓名",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "middle-page/index",
            "style": {
              "navigationBarTitleText": "",
              //"navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "menu-list/index",
            "style": {
              "navigationBarTitleText": "管理我的应用"
            }
          },
          {
            "path": "agreement-gather/user-agreement/index",
            "style": {
              "navigationBarTitleText": "服务协议",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "agreement-gather/arrival-agreement/index",
            "style": {
                "navigationBarTitleText": "陪诊师入驻协议",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
        },
          {
            "path": "agreement-gather/order-agreement/index",
            "style": {
                "navigationBarTitleText": "下单协议",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
        },
          {
            "path": "agreement-gather/exeception-clause/index",
            "style": {
              "navigationBarTitleText": "免责条款",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "application/index",
            "style": {
                "navigationBarTitleText": "陪诊师申请",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
          },
          {
            "path": "application/intro",
            "style": {
                "navigationBarTitleText": "陪诊师申请须知",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
          },
          {
            "path": "agreement-gather/secrecy-policy/index",
            "style": {
              "navigationBarTitleText": "隐秘政策",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "agreement-gather/platform-agreement/index",
            "style": {
              "navigationBarTitleText": "平台服务协议",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "news/platform-news/index",
            "style": {
              "navigationBarTitleText": "平台消息",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "news/platform-news/platform-news-details",
            "style": {
              "navigationBarTitleText": "平台消息详情",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "news/campus-news/index",
            "style": {
              "navigationBarTitleText": "租户消息",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root": "modules/business",
        "pages": [
          {
            "path": "chatLogin/index",
            "style": {
              "navigationBarTitleText": "用户登录",
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "consult/index",
            "style": {
              "navigationBarTitleText": "我的咨询",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "chat-record/index",
            "style": {
              "navigationBarTitleText": "聊天记录",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "case/detail",
            "style": {
              "navigationBarTitleText": "病例详情",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "chat/index",
            "style": {
              "navigationBarTitleText": "聊天窗口",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "notice/advice-detail",
            "style": {
              "navigationBarTitleText": "通知详情",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "information/detail",
            "style": {
              //"navigationBarTitleText": "资讯详情",
              "navigationBarTitleText": "",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "information/index",
            "style": {
              "navigationBarTitleText": "资讯列表",
  //            "navigationBarTitleText": "",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "patients/index",
            "style": {
              "navigationBarTitleText": "家庭就诊人",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "patients/add",
            "style": {
              "navigationBarTitleText": "添加家庭就诊人",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "patients/addTep",
            "style": {
              "navigationBarTitleText": "患者档案添加",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root": "modules/community",
        "pages": [
          {
            "path": "posts/detail/index",
            "style": {
              "navigationBarTitleText": "帖子详情",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/gambit/index",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/post-gambit/index",
            "style": {
              "navigationBarTitleText": "选择话题",
              "enablePullDownRefresh": false,
              // "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/post-message/index",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/edit/index",
            "style": {
              "navigationBarTitleText": "编辑帖子",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/invite-reply/index",
            "style": {
              "navigationBarTitleText": "邀请医生解答",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/reply/index",
            "style": {
              "navigationBarTitleText": "帖子回复",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "posts/invite-record/index",
            "style": {
              "navigationBarTitleText": "邀请记录",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "news/reply/index",
            "style": {
              "navigationBarTitleText": "回复",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "news/like-collect/index",
            "style": {
              "navigationBarTitleText": "点赞和收藏",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "enterprise-wechat-group/index",
            "style": {
              "navigationBarTitleText": "企业微信群",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "enterprise-wechat-group/check/index",
            "style": {
              "navigationBarTitleText": "查看群二维码",
              "enablePullDownRefresh": false,
              // #ifdef H5
              "navigationStyle": "custom",
              // #endif
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "circle/more/index",
            "style": {
              "navigationBarTitleText": "更多圈子",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "circle/index",
            "style": {
              "navigationBarTitleText": "圈子详情",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              },
              "mp-alipay": {
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "personal/my-comment/index",
            "style": {
              "navigationBarTitleText": "我的评论",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "personal/my-like/index",
            "style": {
              "navigationBarTitleText": "我的点赞",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "personal/my-collect/index",
            "style": {
              "navigationBarTitleText": "我的收藏",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "personal/my-posts/index",
            "style": {
              "navigationBarTitleText": "我的帖子",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "personal/home-page/index",
            "style": {
              // #ifndef MP-ALIPAY
              "navigationBarTitleText": "我的",
              // #endif
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              },
              "mp-alipay": {
                "allowsBounceVertical": "NO",
                // 将回弹属性关掉
                "bounce": "none",
                "titleNView": false,
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "patronsaint/index",
            "style": {
              "navigationBarTitleText": "健康保护神",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "circle/guide-subscribe/index",
            "style": {
              "navigationBarTitleText": "圈子推荐",
              "navigationStyle": "custom",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "post-subject/index",
            "style": {
              "navigationBarTitleText": "帖子专题",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "scratch/index",
            "style": {
              "navigationStyle": "custom",
              "navigationBarTitleText": "活动专区",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root":"modules/directseeding",
        "pages": [
          // directseeding
          {
            "path": "video-list/index",
            "style": {
              "navigationBarTitleText": "葫芦直播",
              "navigationStyle": "custom",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "shortVideo/list/index",
            "style": {
			  "navigationBarTextStyle": "white",
              "navigationBarTitleText": "直播列表",
              "enablePullDownRefresh": false,
               "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "wxVideo/index",
            "style": {
              "navigationBarTitleText": "微信直播列表",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "post-video/index",
            "style": {
              "navigationBarTitleText": "帖子视频列表",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          // 直播结束页面
          {
            "path": "live-compute/index",
            "style": {
              "navigationBarTitleText": "直播结束",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
          // {
          //   "path": "seedingVideo/index",
          //   "style": {
          //     "navigationBarTitleText": "直播间",
          //     "enablePullDownRefresh": false,
          //     "app-plus": {
          //       "titleNView": {
          //         "backgroundColor": "#fff"
          //       }
          //     }
          //   }
          // }

        ]

      },
      {
        "root":"modules/activity",
        "pages": [
          {
            "path": "calabash/calabashWebview",
            "style": {
              "navigationBarTitleText": "小葫芦"
            }
          },
          {
            "path": "calabash/index",
            "style": {
              "navigationBarTitleText": "小葫芦",
              "navigationStyle": "custom"
            }
          },
          {
            "path": "calabash/myLuckyCoin",
            "style": {
              "navigationBarTitleText": "我的福币",
              "navigationStyle": "custom"
            }
          },
          {
            "path": "calabash/luckyCoinaTask",
            "style": {
              "navigationBarTitleText": "福币任务",
              "navigationStyle": "custom",
              "mp-alipay": {
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "calabash/exchangeWings/integrationShop",
            "style": {
              "navigationBarTitleText": "兑换好礼"
            }
          },
          {
            "path": "calabash/exchangeWings/integrationGoods",
            "style": {
              "navigationBarTitleText": "兑换详情",
              "navigationStyle": "custom"
            }
          },
          {
            "path": "calabash/exchangeWings/exchangeRecords",
            "style": {
              "navigationBarTitleText": "兑换记录"
            }
          },
          {
            "path": "calabash/exchangeWings/meAddress",
            "style": {
              "navigationBarTitleText": "我的地址"
            }
          },
          {
            "path": "calabash/exchangeWings/addAddress",
            "style": {
              "navigationBarTitleText": "新增地址"
            }
          },
          {
            //
            "path": "pages/accurate-promotion-of-small-gourd/index",
            "style": {
              "navigationBarTitleText": "小葫芦精准推广",
              "enablePullDownRefresh": false,
               "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/diagnosis/list",
            "style": {
              "navigationBarTitleText": "医院服务评价",
              "enablePullDownRefresh": false,
               "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/diagnosis/add",
            "style": {
              "navigationBarTitleText": "医院服务评价",
              "enablePullDownRefresh": false,
               "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/diagnosis/subscribemessage",
            "style": {
              "navigationBarTitleText": "医院服务满意度调查",
              "enablePullDownRefresh": false,
               "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "research/index",
            "style": {
              "navigationBarTitleText": "问卷记录",
              // #ifdef H5
              "navigationStyle": "custom",
              // #endif
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "research/detail",
            "style": {
              "navigationBarTitleText": "调研详情",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "questionnaire/index",
            "style": {
              "navigationBarTitleText": "问卷详情",
              // #ifdef H5
              "navigationStyle": "custom",
              // #endif
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "questionnaire/components/fillin",
            "style": {
              "navigationBarTitleText": "编辑问卷",
              // #ifdef H5
              "navigationStyle": "custom",
              // #endif
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "hospital-ranking/index",
            "style": {
              "navigationBarTitleText": "医院点评",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "hospital-ranking/hospital-detail/index",
            "style": {
              "navigationBarTitleText": "医院详情",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "hospital-ranking/doctor-detail/index",
            "style": {
              "navigationBarTitleText": "医生详情",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "hospital-ranking/remark/index",
            "style": {
              "navigationBarTitleText": "点评",
              "navigationStyle": "custom",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "health-testing/index",
            "style": {
              "navigationBarTitleText": "健康自测",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "health-testing/testing-detail",
            "style": {
              "navigationBarTitleText": "健康自测详情",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "health-testing/my-evaluating",
            "style": {
              "navigationBarTitleText": "我的评测",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "user-activity/index",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationBarBackgroundColor": "#00B484",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "user-activity/comment-details",
            "style": {
              "navigationBarTitleText": "评论明细",
              "enablePullDownRefresh": false,
              "navigationBarTextStyle": "white",
              "navigationBarBackgroundColor": "#00B484",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "user-activity/like-details",
            "style": {
              "navigationBarTitleText": "点赞明细",
              "enablePullDownRefresh": false,
              "navigationBarTextStyle": "white",
              "navigationBarBackgroundColor": "#00B484",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]

      },
      {
        "root":"modules/pharmacy",
        "pages": [
          {
            "path": "pharmacy-cyclopedia/index",
            "style": {
              // "navigationBarTitleText": "用药指南",
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/doctor-question/index",
            "style": {
              "navigationBarTitleText": "药师问答",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/pharmacy-evaluate/index",
            "style": {
              "navigationBarTitleText": "病友分享",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/look-more/index",
            "style": {
              "navigationBarTitleText": "查看更多",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/enterprise/index",
            "style": {
              "navigationBarTitleText": "企业介绍",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/drugstore/index",
            "style": {
              "navigationBarTitleText": "附近药店",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/pharmacy-remind/index",
            "style": {
              "navigationBarTitleText": "用药提醒",
              "enablePullDownRefresh": false,
              "navigationBarBackgroundColor":"#f0f2f2",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#f0f2f2"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/pharmacy-remind/add-page",
            "style": {
              "navigationBarTitleText": "创建提醒",
              "navigationBarBackgroundColor":"#f0f2f2",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#f0f2f2"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/pharmacy-remind/pharmacy-detail-page",
            "style": {
              "navigationBarTitleText": "用药详情",
              "navigationBarBackgroundColor":"#f0f2f2",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#f0f2f2"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/publish-post/index",
            "style": {
              "navigationBarTitleText": "发帖",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/history/index",
            "style": {
              "navigationBarTitleText": "我的说明书",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/history/poster-picture",
            "style": {
              "navigationBarTitleText": "【红卡】中国专家共识",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/history/free-get-glareme",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/electronic-book/index",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/electronic-book/detail",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  // "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/index",
            "style": {
              // #ifndef MP-ALIPAY
              "navigationBarTitleText": "每日辟谣",
              // #endif
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              },
              "mp-alipay": {
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/more-rumour",
            "style": {
              "navigationBarTitleText": "更多辟谣",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/poster",
            "style": {
              "navigationBarTitleText": "每日辟谣海报",
              "navigationBarBackgroundColor":"#BFEADE",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#BFEADE"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/web-white-video/index",
            "style": {
              "navigationBarTitleText": "视频播放",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/free-get-poster",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/rumour-ranking-list",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              },
              "mp-alipay": {
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/rumour-rule",
            "style": {
              "navigationBarTitleText": "辟谣规则",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/history-topic-analysis",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              },
              "mp-alipay": {
                "transparentTitle": "always",
                "titlePenetrate": "YES"
              }
            }
          },
          {
            "path": "pharmacy-cyclopedia/everyday-rumour/activity-prefecture",
            "style": {
              "navigationBarTitleText": "",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root":"modules/foreign",
        "pages": [
          {
            "path": "guide",
            "style": {
              "navigationBarTitleText": " ",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "target",
            "style": {
              "navigationBarTitleText": "免费咨询",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
          ]
      },
      {
                "root": "modules/accompany-doctor",
                "pages": [
                  {
                    "path": "distribute/application-distribution",
                    "style": {
                      "navigationBarTitleText": "申请分销",
                      "navigationStyle": "custom",
                      "app-plus": {
                          "titleNView": {
                              "backgroundColor": "#fff"
                          }
                      }
                    }
                  },
                  {
                      "path": "storeManagement/index",
                      "style": {
                          "navigationBarTitleText": "门店管理",
                          "enablePullDownRefresh": false,
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#fff"
                              }
                          }
                      }
                  },
                  {
                      "path": "storeManagement/fixtures",
                      "style": {
                          "navigationBarTitleText": "一键装修",
                          "enablePullDownRefresh": false,
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#fff"
                              }
                          }
                      }
                  },
                  {
                      "path": "hospital-ranking/index",
                      "style": {
                          "navigationBarTitleText": "医院点评",
                          "enablePullDownRefresh": false,
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#fff"
                              }
                          }
                      }
                  },
                    {
                        "path": "server/withdraw",
                        "style": {
                            "navigationBarTitleText": "提现",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "server/balances",
                        "style": {
                            "navigationBarTitleText": "我的余额",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "combo-detail/index",
                        "style": {
                            "navigationBarTitleText": "套餐详情",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "combo-order/index",
                        "style": {
                            "navigationBarTitleText": "套餐订单",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "my-combo/index",
                        "style": {
                            "navigationBarTitleText": "订单",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/index",
                        "style": {
                            // #ifndef MP-ALIPAY
                            "navigationBarTitleText": "",
                            // #endif
                            "enablePullDownRefresh": false,
                            "navigationBarBackgroundColor": "#F4F6FA",
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            },
                            "mp-alipay": {
                              "transparentTitle": "always",
                              "titlePenetrate": "YES"
                            }
                        }
                    },
                    {
                        "path": "home/transfers",
                        "style": {
                            "navigationBarTitleText": "云陪诊",
                            "enablePullDownRefresh": false,
                            "navigationBarBackgroundColor": "#F4F6FA",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/personal/my-collect/index",
                        "style": {
                            "navigationBarTitleText": "我的收藏",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/personal/my-posts/index",
                        "style": {
                            "navigationBarTitleText": "我的帖子",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/personal/my-comment/index",
                        "style": {
                            "navigationBarTitleText": "我的评论",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/personal/my-like/index",
                        "style": {
                            "navigationBarTitleText": "我的点赞",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/news/like-collect/index",
                        "style": {
                            "navigationBarTitleText": "点赞与收藏",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "home/components/news/reply/index",
                        "style": {
                            "navigationBarTitleText": "我的回复",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "accompany/home/<USER>",
                        "style": {
                            "navigationBarTitleText": "陪诊师首页",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },

                    {
                      "path": "accompany/home/<USER>",
                      "style": {
                          "navigationBarTitleText": "海报",
                          "enablePullDownRefresh": false,
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#F4F6FA"
                              }
                          }
                      }
                    },


                    {
                        "path": "accompany/update-data/index",
                        "style": {
                            "navigationBarTitleText": "资料修改",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },
                    {
                        "path": "accompany/update-data/update-user-info",
                        "style": {
                            "navigationBarTitleText": "资料修改",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#fff"
                                }
                            }
                        }
                    },


                    {
                        "path": "system-search/index",
                        "style": {
                            "navigationBarTitleText": "搜索",
                            "app-plus": {
                                "titleNView": false
                            }
                        }
                    },
                    {
                      "path": "webView/webView",
                      "style": {
                          "navigationBarTitleText": "外部网页",
                          "app-plus": {
                              "titleNView": false
                          }
                      }
                  },
                    {
                        "path": "system-search/search-data",
                        "style": {
                            "navigationBarTitleText": "搜索",
                            "app-plus": {
                                "titleNView": false
                            }
                        }
                    },
                    {
                      "path": "system-search/accompany-teacher",
                      "style": {
                          "navigationBarTitleText": "社区交流",
                          "enablePullDownRefresh": false,
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#F4F6FA"
                              }
                          }
                      }
                  },
                  {
                    "path": "system-search/accompany-list",
                    "style": {
                        "navigationBarTitleText": "陪诊师",
                        "enablePullDownRefresh": false,
                        "app-plus": {
                            "titleNView": {
                                "backgroundColor": "#F4F6FA"
                            }
                        }
                    }
                },
                {
                    "path": "system-search/accompany-details",
                    "style": {
                        "navigationBarTitleText": "陪诊师",
                        "enablePullDownRefresh": false,
                        "navigationStyle": "custom",
                        "app-plus": {
                            "titleNView": {
                                "backgroundColor": "#F4F6FA"
                            }
                        }
                    }
                },
                    {
                        "path": "system-search/doctor-list",
                        "style": {
                            "navigationBarTitleText": "本地名医",
                            "enablePullDownRefresh": false,
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "service-detail/index",
                        "style": {
                            "navigationBarTitleText": "",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    },
                    {
                        "path": "service-reservation/index",
                        "style": {
                            // #ifndef MP-ALIPAY
                            "navigationBarTitleText": "陪诊服务",
                            // #endif
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            },
                            "mp-alipay": {
                              "transparentTitle": "always",
                              "titlePenetrate": "YES"
                            }
                        }
                    },
                    {
                      "path": "service-reservation/insuranceList",
                      "style": {
                          "navigationBarTitleText": "门诊无忧服务订单",
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#fff"

                              }
                          }
                      }
                  },
                    {
                      "path": "service-reservation/add-patient/index",
                      "style": {
                          "navigationBarTitleText": "添加就诊人",
                          "enablePullDownRefresh": false,
                          "navigationStyle": "custom",
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#F4F6FA"
                              }
                          }
                      }
                  },
                  {
                      "path": "service-reservation/add-patient/add",
                      "style": {
                          "navigationBarTitleText": "新增就诊人",
                          "enablePullDownRefresh": false,
                          "navigationStyle": "custom",
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#F4F6FA"
                              }
                          }
                      }
                  },
                  {
                      "path": "service-reservation/add-patient/edit",
                      "style": {
                          "navigationBarTitleText": "编辑就诊人",
                          "enablePullDownRefresh": false,
                          "navigationStyle": "custom",
                          "app-plus": {
                              "titleNView": {
                                  "backgroundColor": "#F4F6FA"
                              }
                          }
                      }
                  },

                    {
                        "path": "service-reservation/service-message/index",
                        "style": {
                            "navigationBarTitleText": "服务信息",
                            "enablePullDownRefresh": false,
                            "navigationStyle": "custom",
                            "app-plus": {
                                "titleNView": {
                                    "backgroundColor": "#F4F6FA"
                                }
                            }
                        }
                    }
                ]
      },
      {
        "root": "modules/provider-management",
        "pages": [
          {
            "path": "pages/dashboard/index",
            "style": {
              "navigationBarTitleText": "工作台",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "pages/update-data/index",
            "style": {
              "navigationBarTitleText": "资料修改",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/update-data/update-user-info",
            "style": {
              "navigationBarTitleText": "资料修改",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/data-analytics/index",
            "style": {
              "navigationBarTitleText": "数据面板",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "pages/order-center/index",
            "style": {
              "navigationBarTitleText": "订单中心",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "pages/order-details/index",
            "style": {
              "navigationBarTitleText": "订单详情",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": false
              }
            }
          },
          {
            "path": "pages/create-order/index",
            "style": {
              "navigationBarTitleText": "创建订单",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#F4F6FA"
                }
              }
            }
          },
          {
            "path": "pages/accompany-record/index",
            "style": {
              "navigationBarTitleText": "陪诊记录",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
        "root": "modules/accompany-doctor-course",
        "pages": [
          {
            "path": "accompany/course/index",
            "style": {
              "navigationBarTitleText": "课程",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/course-detail",
            "style": {
              "navigationBarTitleText": "课程详情",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/course-my",
            "style": {
              "navigationBarTitleText": "我的课程",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/second-classify",
            "style": {
              "navigationBarTitleText": "二级分类名称",
              "navigationBarBackgroundColor": "#F4F6FA",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/course-search",
            "style": {
              "navigationBarTitleText": "课程搜索",
              "navigationBarBackgroundColor": "#F4F6FA",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/recently-study",
            "style": {
              "navigationBarTitleText": "最近学习",
              "enablePullDownRefresh": false,
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          },
          {
            "path": "accompany/course/study-statement",
            "style": {
              "navigationBarTitleText": "学习报表",
              "enablePullDownRefresh": false,
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }
        ]
      },
      {
                "root": "modules/distribute",
                "pages": [
                ]
      },
      {
            "root": "modules/distribution",
            "pages": [
              {
                "path": "index",
                "style": {
                  "navigationBarTitleText": "分销管理",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "apply/index",
                "style": {
                  "navigationBarTitleText": "申请成为分销员",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "distributionPoster/index",
                "style": {
                  "navigationBarTitleText": "分销海报",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "distributionPoster/detail",
                "style": {
                  "navigationBarTitleText": "海报详情",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "myCustomers/index",
                "style": {
                  "navigationBarTitleText": "我的客户",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "myCustomers/detail",
                "style": {
                  "navigationBarTitleText": "客户详情",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "withdrawalRecord/index",
                "style": {
                  "navigationBarTitleText": "提现记录",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "withdrawalRecord/detail",
                "style": {
                  "navigationBarTitleText": "提现详情",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                "path": "distributionWithdrawal/index",
                "style": {
                  "navigationBarTitleText": "分销提现",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              },
              {
                  "path": "distributStatement/index",
                  "style": {
                      "navigationBarTitleText": "分账流水",
                      "navigationStyle": "custom",
                      "app-plus": {
                          "titleNView": {
                              "backgroundColor": "#fff"
                          }
                      }
                  }
              },
              {
                "path": "distributStatement/detail",
                "style": {
                  "navigationBarTitleText": "分账详情",
                  "navigationStyle": "custom",
                  "app-plus": {
                      "titleNView": {
                          "backgroundColor": "#fff"
                      }
                  }
                }
              },
              {
                  "path": "log/index",
                  "style": {
                    "navigationBarTitleText": "分销记录",
                    "navigationStyle": "custom",
                    "app-plus": {
                      "titleNView": {
                        "backgroundColor": "#fff"
                      }
                    }
                  }
              },
              {
                "path": "client/bind-client/poster",
                "style": {
                  "navigationBarTitleText": "关联客户",
                  "navigationStyle": "custom",
                  "app-plus": {
                    "titleNView": {
                      "backgroundColor": "#fff"
                    }
                  }
                }
              }
            ]
      }
      // #ifdef MP-WEIXIN
      ,{
        "root": "modules/packet",
        "pages": [
          {
            "path": "wx-full-screen-ad/index",
            "style": {
              "navigationBarTitleText": "",
              "navigationStyle": "custom",
              "enablePullDownRefresh": false,
              "navigationBarTextStyle": "white",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
          }

        ]
      }
      // #endif
    ],
    "tabBar": {
      // #ifdef MP-WEIXIN
      "custom": true,
      // #endif
      "color": "#B8BABC",
      "selectedColor": "#00D29D",
      "backgroundColor": "#ffffff",
      "list": [{
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "static/image/business/icon-home.png",
        "selectedIconPath": "static/image/business/icon-home-active.png"
      },
        {
          "pagePath": "pages/circle-home/index",
          "text": "交流",
          "iconPath": "static/image/business/icon-exchange.png",
          "selectedIconPath": "static/image/business/icon-exchange-active.png"
        },
        {
          "pagePath": "pages/post-message/index",
          "text": "发帖",
          "iconPath": "static/image/business/icon-plus.png",
          "selectedIconPath": "static/image/business/icon-plus.png"
        },
        {
          "pagePath": "pages/news/index",
          "text": "消息",
          "iconPath": "static/image/business/icon-message.png",
          "selectedIconPath": "static/image/business/icon-message-active.png"
        },
        {
          "pagePath": "pages/personal/index",
          "text": "我的",
          "iconPath": "static/image/business/icon-my.png",
          "selectedIconPath": "static/image/business/icon-my-active.png"
        }
      ],
      "borderStyle": "white"
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#FFFFFF",
        "backgroundColor": "#F8F8F8",
        "backgroundColorTop": "#fff",
        "backgroundColorBottom": "#F4F5F6",
        "mp-alipay": {
          "allowsBounceVertical": "NO",
          "titleBarColor": "#FFFFFF"
        }
        // #ifdef MP-ALIPAY
        ,"usingComponents": {
          "ad-lights": "/mycomponents/ad/index",
          "private-pouch-component-pop": "/mycomponents/private-pouch-component-pop/index"
        }
        // #endif
    },
    "style": {
        "app-plus": {
            "animationType": "slide-in-right",
            "animationDuration": 300
        }
    },
    "condition": {
        //模式配置，仅开发期间生效
        "current": 0,
        //当前激活的模式(list 的索引项)
        "list": [{
            "name": "",
            //模式名称
            "path": "",
            //启动页面，必选
            "query": ""
                //启动参数，在页面的onLoad函数里面得到
        }]
    },
    "preloadRule":{
      "pages/index/index":{
        "network": "all",
        "packages": ["modules/accompany-doctor", "modules/provider-management"]
      }
    },
    "plugins": {
        "live-player-plugin": {
            "version": "1.3.2", // 注意填写该直播组件最新版本号，微信开发者工具调试时可获取最新版本号（复制时请去掉注释）
            "provider": "wx2b03c6e691cd7370" // 必须填该直播组件appid，该示例值即为直播组件appid（复制时请去掉注释）
        }
    }
}
